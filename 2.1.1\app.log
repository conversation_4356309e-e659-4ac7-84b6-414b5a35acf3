2025-05-30 23:26:42,782 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-30 23:26:42,786 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml 不存在，正在自动生成默认配置...
2025-05-30 23:26:42,787 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:26:42,789 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:26:42,817 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:26:42,818 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:26:53,969 - INFO - API密钥验证成功
2025-05-30 23:26:53,999 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:26:53,999 - INFO - 已将加密的API密钥保存到配置文件
2025-05-30 23:26:53,999 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:26:54,080 - WARNING - 模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml 不存在，正在生成默认配置...
2025-05-30 23:26:54,082 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:26:54,141 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:26:54,275 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:26:54,276 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:26:54,277 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-30 23:26:54,410 - INFO - 语言模式配置已加载。
2025-05-30 23:26:54,410 - INFO - 语言模式配置已加载。
2025-05-30 23:26:54,411 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-30 23:26:54,411 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-30 23:26:54,424 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-30 23:26:54,435 - INFO - 控制台线程已启动，准备进入循环。
2025-05-30 23:26:54,435 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 23:26:54,436 - INFO - 菜单已显示，等待用户输入。
2025-05-30 23:26:54,501 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-30 23:26:56,096 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 23:26:57,424 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 23:26:57,425 - INFO - API服务正常
2025-05-30 23:27:07,649 - INFO - 用户输入: 0
2025-05-30 23:27:12,540 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:27:12,541 - INFO - 🔧 调试模式已开启 - 将显示详细的翻译信息
2025-05-30 23:27:14,865 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:27:14,866 - INFO - 思考预算已更新为：0 Tokens（思考模式已关闭）
2025-05-30 23:27:19,075 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 23:27:19,079 - INFO - 菜单已显示，等待用户输入。
2025-05-30 23:27:22,945 - INFO - 用户输入: 2
2025-05-30 23:27:22,973 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 23:27:22,974 - INFO - 已切换到翻译模式 2: 中文-韩文-敬语
2025-05-30 23:27:23,088 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 23:27:23,090 - INFO - 菜单已显示，等待用户输入。
2025-05-30 23:27:33,503 - INFO - 检测到三次空格，触发翻译
2025-05-30 23:27:33,505 - INFO - 【原文】
네 미화씨도 좋은꿈꾸세요 ^^
2025-05-30 23:27:33,507 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 97, 3754.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 23:27:33,508 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.885)]
2025-05-30 23:27:33,508 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 23:27:33,508 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 23:27:33,508 - INFO - 检测到原文语言: ko
2025-05-30 23:27:33,508 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 23:27:33,509 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 23:27:33,509 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 23:27:33,509 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 23:27:33,509 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 23:27:33,509 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 23:27:33,509 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


将以下内容从韩文翻译成中文：
네 미화씨도 좋은꿈꾸세요 ^^
2025-05-30 23:27:33,509 - DEBUG - 【构建提示词】长度: 602 字符
2025-05-30 23:27:33,510 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-30 23:27:33,519 - INFO - 🔄 显示GUI进度指示器
2025-05-30 23:27:33,699 - DEBUG - API密钥解密成功
2025-05-30 23:27:33,700 - DEBUG - API密钥解密成功
2025-05-30 23:27:33,700 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 23:27:33,700 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 23:27:33,701 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 23:27:33,712 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-30 23:27:33,712 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-30 23:27:33,713 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n将以下内容从韩文翻译成中文：\n네 미화씨도 좋은꿈꾸세요 ^^"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64,
    "thinkingConfig": {
      "thinkingBudget": 0
    }
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-30 23:27:34,578 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "好的，美花小姐也做个好梦哦 ^^"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 399,
    "candidatesTokenCount": 12,
    "totalTokenCount": 411,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 399
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "VsA5aICjG9TM1MkPotbnmQk"
}

2025-05-30 23:27:34,578 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '好的，美花小姐也做个好梦哦 ^^'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 399, 'candidatesTokenCount': 12, 'totalTokenCount': 411, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 399}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'VsA5aICjG9TM1MkPotbnmQk'}
2025-05-30 23:27:34,579 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 23:27:34,579 - DEBUG -   - 思考Token数: 0
2025-05-30 23:27:34,579 - DEBUG -   - 提示Token数: 399
2025-05-30 23:27:34,580 - DEBUG -   - 输出Token数: 12
2025-05-30 23:27:34,580 - DEBUG -   - 总Token数: 411
2025-05-30 23:27:34,581 - DEBUG - pycld2 检测结果: is_reliable=False, details=(('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 23:27:34,581 - DEBUG - 基于特征补充候选: zh (score: 0.3750)
2025-05-30 23:27:34,581 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.375)]
2025-05-30 23:27:34,581 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 23:27:34,581 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 23:27:34,581 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 23:27:35,039 - DEBUG - 输入框内容已替换为: 好的，美花小姐也做个好梦哦 ^^
2025-05-30 23:27:35,047 - INFO - 【翻译结果】
好的，美花小姐也做个好梦哦 ^^
2025-05-30 23:27:35,048 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 23:27:35,048 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 23:27:35,048 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 23:27:35,050 - INFO - 已立即保存 2 条缓存记录
2025-05-30 23:27:35,054 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 23:27:35,550 - INFO - ✅ 隐藏GUI进度指示器
2025-05-30 23:28:24,960 - INFO - 检测到三次空格，触发翻译
2025-05-30 23:28:24,961 - INFO - 【原文】
네, 잘 자요
2025-05-30 23:28:24,962 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 93, 3276.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 23:28:24,962 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.715)]
2025-05-30 23:28:24,962 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 23:28:24,962 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 23:28:24,962 - INFO - 检测到原文语言: ko
2025-05-30 23:28:24,962 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 23:28:24,963 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 23:28:24,963 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 23:28:24,963 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 23:28:24,963 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 23:28:24,963 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 23:28:24,963 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-30 23:28:24,963 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 네 미화씨도 좋은꿈꾸세요 ^^
翻译: 好的，美花小姐也做个好梦哦 ^^

将以下内容从韩文翻译成中文：
네, 잘 자요
2025-05-30 23:28:24,964 - DEBUG - 【构建提示词】长度: 662 字符
2025-05-30 23:28:24,964 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-30 23:28:24,967 - INFO - 🔄 显示GUI进度指示器
2025-05-30 23:28:25,178 - DEBUG - API密钥解密成功
2025-05-30 23:28:25,179 - DEBUG - API密钥解密成功
2025-05-30 23:28:25,179 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 23:28:25,179 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 23:28:25,180 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 23:28:25,181 - DEBUG - 【API请求JSON】模型: gemini-2.5-flash-preview-04-17, URL: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent?key=AIzaSyBQ8qQ_SdNH3TOrdrAPO-AJqLVmDqd5sxA
2025-05-30 23:28:25,181 - DEBUG - 【API请求JSON】请求头: {'Content-Type': 'application/json'}
2025-05-30 23:28:25,182 - DEBUG - 【API请求JSON】请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "\n你是一个专业的多语言翻译专家，精通多种语言的互译。\n翻译规则如下：\n- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 \n- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。\n- 若无法准确检测输入语言，返回\"语言检测错误，请明确指定输入语言\"。\n- 严格要求：\n  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。\n- 语气词翻译规则：\n    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词\n    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词\n    * 保持原文的语气强度和情感色彩，不夸大也不减弱\n  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。\n  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。\n  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。\n  - 保持原文语义完整，遵循翻译理论中的三个核心\"信达雅\"，不增删核心内容。\n  - 根据用户输入或对话历史中的上下文，确保翻译一致。\n- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！\n\n\n以下是最近的上下文，请参考上下文翻译以保持一致性：\n原文: 네 미화씨도 좋은꿈꾸세요 ^^\n翻译: 好的，美花小姐也做个好梦哦 ^^\n\n将以下内容从韩文翻译成中文：\n네, 잘 자요"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "maxOutputTokens": 2048,
    "topP": 0.85,
    "topK": 64,
    "thinkingConfig": {
      "thinkingBudget": 0
    }
  },
  "safetySettings": [
    {
      "category": "HARM_CATEGORY_HARASSMENT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_HATE_SPEECH",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
      "threshold": "BLOCK_NONE"
    },
    {
      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
      "threshold": "BLOCK_NONE"
    }
  ]
}
2025-05-30 23:28:26,594 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "好的，晚安"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 439,
    "candidatesTokenCount": 4,
    "totalTokenCount": 443,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 439
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "isA5aIv2G-S31MkPwvq82A0"
}

2025-05-30 23:28:26,594 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '好的，晚安'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 439, 'candidatesTokenCount': 4, 'totalTokenCount': 443, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 439}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'isA5aIv2G-S31MkPwvq82A0'}
2025-05-30 23:28:26,595 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 23:28:26,595 - DEBUG -   - 思考Token数: 0
2025-05-30 23:28:26,595 - DEBUG -   - 提示Token数: 439
2025-05-30 23:28:26,596 - DEBUG -   - 输出Token数: 4
2025-05-30 23:28:26,596 - DEBUG -   - 总Token数: 443
2025-05-30 23:28:26,597 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('ChineseT', 'zh-Hant', 93, 1755.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 23:28:26,597 - DEBUG - 基于特征补充候选: zh (score: 0.4000)
2025-05-30 23:28:26,597 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.4)]
2025-05-30 23:28:26,597 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 23:28:26,597 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 23:28:26,597 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 23:28:27,050 - DEBUG - 输入框内容已替换为: 好的，晚安
2025-05-30 23:28:27,052 - INFO - 【翻译结果】
好的，晚安
2025-05-30 23:28:27,052 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 23:28:27,053 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 23:28:27,053 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 23:28:27,054 - INFO - 已立即保存 2 条缓存记录
2025-05-30 23:28:27,057 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 23:28:27,560 - INFO - ✅ 隐藏GUI进度指示器
2025-05-30 23:31:02,501 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-30 23:31:02,707 - INFO - 语言模式配置已加载。
2025-05-30 23:31:02,707 - INFO - 语言模式配置已加载。
2025-05-30 23:31:02,707 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-30 23:31:02,707 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-30 23:31:02,713 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-30 23:31:02,726 - INFO - 控制台线程已启动，准备进入循环。
2025-05-30 23:31:02,726 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 23:31:02,728 - INFO - 菜单已显示，等待用户输入。
2025-05-30 23:31:02,800 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-30 23:31:03,320 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 23:31:03,552 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 23:31:03,553 - INFO - API服务正常
2025-05-30 23:31:10,277 - INFO - 检测到三次空格，触发翻译
2025-05-30 23:31:10,279 - INFO - 【原文】
아. 이제야 메시지 답장하네요~ 저는 씻고 쉬어야겠어요. 내일 메시지 보내는 건 어때요?
2025-05-30 23:31:10,281 - INFO - 检测到原文语言: ko
2025-05-30 23:31:10,281 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 23:31:10,281 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 23:31:10,290 - INFO - 🔄 显示GUI进度指示器
2025-05-30 23:31:12,269 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 23:31:12,727 - INFO - 【翻译结果】
哦，我现在才回复你的消息呀~ 我得去洗澡休息了。明天再发消息怎么样？
2025-05-30 23:31:12,728 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 23:31:12,728 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 23:31:12,729 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 23:31:12,730 - INFO - 已立即保存 2 条缓存记录
2025-05-30 23:31:12,735 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 23:31:13,235 - INFO - ✅ 隐藏GUI进度指示器
